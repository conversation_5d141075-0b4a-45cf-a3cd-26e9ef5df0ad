import logging
import traceback
from datetime import datetime, timed<PERSON>ta
from typing import Tu<PERSON>, List, Dict, Any
import re
import pytz
from slack_sdk.socket_mode.request import SocketModeRequest

from ..jira_client import Jira<PERSON>lient
from ..config import SUPPORT_SUMMARY_MAX_ISSUES

logger = logging.getLogger(__name__)

# Slack's message limit is 4000 characters
SLACK_MESSAGE_LIMIT = 4000
SUMMARY_CHARACTER_LIMIT = 45


CUSTOM_FIELD_LOOKUP = {
    "implements": "customfield_10255" # List of dicts where "value" is the implement number formatted as "R123"
}


def split_message_preserving_codeblocks(message: str) -> List[str]:
    if len(message) <= SLACK_MESSAGE_LIMIT:
        return [message]

    chunks = []
    current_chunk = ""
    lines = message.split('\n')
    in_codeblock = False

    for line in lines:
        # Check if this line would make the current chunk too long
        potential_chunk = current_chunk + ('\n' if current_chunk else '') + line

        # Account for potential closing ``` if we're in a codeblock
        potential_length = len(potential_chunk)
        if in_codeblock:
            potential_length += 4  # \n```

        if potential_length > SLACK_MESSAGE_LIMIT and current_chunk:
            # Need to split here
            if in_codeblock:
                # Close the codeblock in current chunk
                current_chunk += '\n```'

            chunks.append(current_chunk)

            # Start new chunk
            if in_codeblock:
                # Reopen codeblock in new chunk
                current_chunk = '```\n' + line
            else:
                current_chunk = line
        else:
            # Add line to current chunk
            if current_chunk:
                current_chunk += '\n'
            current_chunk += line

        # Track if we're entering or leaving a codeblock
        if line.strip() == '```':
            in_codeblock = not in_codeblock

    # Add the final chunk
    if current_chunk:
        chunks.append(current_chunk)

    return chunks


def format_support_summary(issues: List[Dict[str, Any]], title: str = "Support Summary", max_issues: int = 100) -> str:
    if not issues:
        return f"{title}:\nNo issues found."

    total_issues = len(issues)
    issues_to_process = issues[:max_issues] if total_issues > max_issues else issues

    # First loop: gather all display information
    issue_data = []
    for issue in issues_to_process:
        fields = issue.get('fields', {})
        key = issue.get('key', 'Unknown')
        summary = fields.get('summary', 'No summary')
        status = fields.get('status', {}).get('name', 'Unknown')

        # Extract robot identifier
        implements = fields.get(CUSTOM_FIELD_LOOKUP["implements"])

        if len(implements) == 0:
            implement = "???"
        else:
            implement = implements[0]["value"]
            
        if implement == "Reaper-200T-Prototype":
            implement = "R2"

        logger.info(f"fields {fields}")

        # Format summary for display - remove robot identifier and truncate
        robot_pattern = r'^[SR]\d+\s*[-:]?\s*'
        summary_display = re.sub(robot_pattern, '', summary, flags=re.IGNORECASE)
        if len(summary_display) > SUMMARY_CHARACTER_LIMIT:
            summary_display = summary_display[:SUMMARY_CHARACTER_LIMIT - 3] + "..."

        # Format status for display
        status_mapping = {
            'done': 'Done',
            'closed': 'Done',
            'resolved': 'Done',
            'in progress': 'In Progress',
            'in review': 'In Review',
            'to do': 'To Do',
            'open': 'Open',
            'new': 'New',
        }
        status_display = status_mapping.get(status.lower(), status)

        issue_data.append({
            'robot_display': implement,
            'summary_display': summary_display,
            'status_display': status_display,
            'key': key
        })

    if not issue_data:
        return f"{title}:\nNo valid issues found."

    issue_data.sort(key=lambda x: x['robot_display'])

    # Calculate column widths based on the longest value in each column
    # Column order: implement, status, summary, key
    max_implement_width = max(len(item['robot_display']) for item in issue_data)
    max_status_width = max(len(item['status_display']) for item in issue_data)
    max_summary_width = max(len(item['summary_display']) for item in issue_data)

    # Add some padding between columns
    implement_width = max_implement_width + 2
    status_width = max_status_width + 2
    summary_width = max_summary_width + 2

    lines = [f"*{title}:*", "```"]

    for item in issue_data:
        # Format each column with proper spacing
        # Column order: implement, status, summary, key
        implement_part = item['robot_display'].ljust(implement_width)
        status_part = item['status_display'].ljust(status_width)
        summary_part = item['summary_display'].ljust(summary_width)
        key_part = item['key']

        line = f"{implement_part}{summary_part}{status_part}{key_part}"
        lines.append(line)

    # Add truncation message if there are more issues than displayed
    if total_issues > max_issues:
        remaining_count = total_issues - max_issues
        lines.append(f"...and {remaining_count} more issues")

    lines.append("```")
    joined_lines = "\n".join(lines)

    return joined_lines


def handle_support_summary_command(request: SocketModeRequest) -> Tuple[bool, List[str]]:
    try:
        # Extract data from request
        command_text = request.payload.get("text", "").strip()
        # Get current time in PST and calculate time period
        pst = pytz.timezone('US/Pacific')
        now = datetime.now(pst)

        # Check if user wants today's issues instead of shift-based
        if command_text.strip().lower() == "today":
            # Get everything from start of today (midnight PST)
            start_of_today = datetime.combine(now.date(), datetime.min.time())
            start_of_today_pst = pst.localize(start_of_today)
            since_datetime = start_of_today_pst.replace(tzinfo=None)
            period_description = "Today"
        else:
            # Use shift-based logic (existing behavior)
            # Configurable shift change times (24-hour format)
            # Examples:
            # [8, 20] = 2 shifts: 8AM-8PM, 8PM-8AM
            # [6, 14, 22] = 3 shifts: 6AM-2PM, 2PM-10PM, 10PM-6AM
            # [6, 12, 18, 0] = 4 shifts: 6AM-12PM, 12PM-6PM, 6PM-12AM, 12AM-6AM
            SHIFT_TIMES = [8, 14, 20]

            # Find the most recent shift change time
            current_hour = now.hour
            sorted_shifts = sorted(SHIFT_TIMES)

            # Find the previous shift time
            previous_shift_hour = None
            previous_shift_date = now.date()

            # Look for the most recent shift time that has passed
            for shift_hour in reversed(sorted_shifts):
                if current_hour >= shift_hour:
                    # Current time is after this shift time today
                    previous_shift_hour = shift_hour
                    break

            if previous_shift_hour is None:
                # Current time is before all shift times today, so use the last shift from yesterday
                previous_shift_hour = sorted_shifts[-1]
                previous_shift_date = (now - timedelta(days=1)).date()

            # Create the previous shift datetime
            previous_shift_time = datetime.combine(previous_shift_date, datetime.min.time().replace(hour=previous_shift_hour))
            previous_shift_time = pst.localize(previous_shift_time)

            # Format time for description
            def format_hour(hour):
                if hour == 0:
                    return "12AM"
                elif hour < 12:
                    return f"{hour}AM"
                elif hour == 12:
                    return "12PM"
                else:
                    return f"{hour-12}PM"

            # Create description
            if previous_shift_date == now.date():
                period_description = f"Since {format_hour(previous_shift_hour)} PST"
            else:
                period_description = f"Since {format_hour(previous_shift_hour)} PST Yesterday"

            # Convert back to naive datetime for Jira API compatibility
            since_datetime = previous_shift_time.replace(tzinfo=None)

        logger.info(f"Generating support summary for period: {period_description} (since {since_datetime})")

        client = JiraClient()

        projects = ["CRS"]
        issues = client.get_issues_modified_since(since_datetime, projects)

        if issues is None:
            error_msg = "Error: Failed to retrieve issues from Jira. Please check the configuration."
            return False, [error_msg]

        # Format the summary
        summary = format_support_summary(issues, f"Support Summary {period_description}", SUPPORT_SUMMARY_MAX_ISSUES)

        # Split the message if it's too long
        message_chunks = split_message_preserving_codeblocks(summary)

        return True, message_chunks

    except Exception as e:
        logger.error(f"Error handling /support-summary command: {e}")
        traceback.print_exc()
        error_msg = "Error: Failed to generate support summary"
        return False, [error_msg]
