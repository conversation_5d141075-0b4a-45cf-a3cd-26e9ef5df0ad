import logging
import traceback
import psycopg
from slack_sdk.socket_mode.request import SocketModeRequest

logger = logging.getLogger(__name__)


def handle_qa_versions_command(
    alexbot_conn: psycopg.Connection,
    request: SocketModeRequest,
) -> tuple[bool, str]:
    """Handle /qa-versions command. Returns success status and response message."""
    try:
        user_name = request.payload.get("user_name")
        # Get all versions where qa=true
        with alexbot_conn.cursor() as cursor:
            cursor.execute(
                """
                SELECT id, version, status
                FROM software_versions
                WHERE qa = true
                ORDER BY version
                """
            )
            results = cursor.fetchall()

        if not results:
            return True, "No QA versions found."

        # Format the response
        response_lines = ["*Software Versions for QA*"]
        for row in results:
            _, version, status = row
            response_lines.append(f"• *{version}* - {status}")

        response_text = "\n".join(response_lines)
        logger.info(f"User @{user_name} requested QA versions list")
        return True, response_text

    except Exception as e:
        logger.error(f"Error in handle_qa_versions_command: {e}")
        traceback.print_exc()
        return False, "Error: Failed to retrieve QA versions"


def handle_qa_versions_update_command(
    alexbot_conn: psycopg.Connection,
    request: SocketModeRequest,
) -> tuple[bool, str]:
    """Handle /qa-versions-update command. Returns success status and response message."""
    try:
        user_name = request.payload.get("user_name")
        command_text = request.payload.get("text", "").strip()
        # Parse command arguments: [version] [qa] [status]
        if not command_text or not command_text.strip():
            return False, "Missing inputs."

        parts = command_text.strip().split()
        if len(parts) < 3:
            return False, "Missing inputs." 
        
        version = parts[0]
        qa_str = parts[1].lower()
        status = " ".join(parts[2:])  # Allow status to contain spaces

        # Validate qa parameter
        if qa_str not in ["true", "false"]:
            return False, "Error: QA flag must be 'true' or 'false'"

        qa_bool = qa_str == "true"

        # Check if version exists, if not create it
        with alexbot_conn.cursor() as cursor:
            cursor.execute(
                "SELECT id FROM software_versions WHERE version = %s",
                (version,)
            )
            existing = cursor.fetchone()

            if existing:
                # Update existing version
                cursor.execute(
                    """
                    UPDATE software_versions
                    SET qa = %s, status = %s
                    WHERE version = %s
                    """,
                    (qa_bool, status, version)
                )
                action = "updated"
            else:
                # Create new version with generated ID
                import uuid
                version_id = str(uuid.uuid4())
                cursor.execute(
                    """
                    INSERT INTO software_versions (id, version, qa, status)
                    VALUES (%s, %s, %s, %s)
                    """,
                    (version_id, version, qa_bool, status)
                )
                action = "created"

            alexbot_conn.commit()

        # After successful update, get and return current state of all QA versions
        with alexbot_conn.cursor() as cursor:
            cursor.execute(
                """
                SELECT id, version, status
                FROM software_versions
                WHERE qa = true
                ORDER BY version
                """
            )
            results = cursor.fetchall()

        if not results:
            response_text = f"Version {action}. No QA versions found."
        else:
            # Format the response similar to qa_versions command
            response_lines = [f"Version {action}. Current QA versions:", "*Software Versions for QA*"]
            for row in results:
                _, version_name, status = row
                response_lines.append(f"• *{version_name}* - {status}")
            response_text = "\n".join(response_lines)

        logger.info(f"User @{user_name} {action} version {version} with qa={qa_bool}, status='{status}'")
        return True, response_text

    except Exception as e:
        logger.error(f"Error in handle_qa_versions_update_command: {e}")
        traceback.print_exc()
        return False, "Error: Failed to update QA version"



