"""
Visualization command handler for <PERSON><PERSON>.

Handles the /viz slash command for requesting robot visualizations.
"""

import logging
import psycopg
from slack_sdk import WebClient
from slack_sdk.socket_mode.request import SocketModeRequest

from ..utils import get_robot_by_channel
from ..visualization_requests import request_visualization

logger = logging.getLogger(__name__)


def handle_viz_command(
    slack_client: WebClient,
    alexbot_conn: psycopg.Connection,
    portal_conn: psycopg.Connection,
    request: SocketModeRequest,
) -> str:
    """Handle /viz command. Returns response message."""
    try:
        # Extract data from request
        user_name = request.payload.get("user_name")
        channel_name = f"#{request.payload.get('channel_name')}"
        robot_id = request.payload.get("text", "").strip() or None
        # Determine robot ID
        if robot_id:
            # Robot was specified as argument
            logger.info(f"User @{user_name} requested visualization for robot {robot_id} in channel {channel_name}")
        else:
            # No robot specified, determine from channel
            robot_id = get_robot_by_channel(portal_conn, channel_name)
            if not robot_id:
                return (
                    f"❌ Error: No robot configured for channel {channel_name}. Please specify a robot: `/viz <robot>`"
                )
            logger.info(f"User @{user_name} requested visualization for channel {channel_name} (robot: {robot_id})")

        # Request visualization (bypass rate limit for /viz commands)
        success = request_visualization(
            alexbot_conn, robot_id, channel_name, requested_by=user_name, bypass_rate_limit=True
        )

        if success:
            return f"✅ Visualization requested for robot `{robot_id}`. You'll receive it in this channel shortly."
        else:
            return f"❌ Visualization request for robot `{robot_id}` failed. Please try again later."
    except Exception as e:
        logger.error(f"Error handling /viz command: {e}")
        return "❌ Error: Failed to process visualization request"
