import logging
import traceback

from slack_sdk.socket_mode import SocketMode<PERSON>lient
from slack_sdk.socket_mode.request import SocketModeRequest
from slack_sdk.socket_mode.response import SocketModeResponse
from slack_sdk import WebClient

from .config import SLACK_TOKEN, SLACK_APP_TOKEN, ALEXBOT_DB_URL, PORTAL_DB_URL, VESELKA_DB_URL
from .utils import get_db_connection
from .commands.viz import handle_viz_command
from .commands.info import handle_info_command
from .commands.qa import handle_qa_command
from .commands.support_summary import handle_support_summary_command
from .commands.qa_versions import (
    handle_qa_versions_command,
    handle_qa_versions_update_command,
)

LOG = logging.getLogger(__name__)

COMMANDS = {
    "/viz": handle_viz_command,
    "/info": handle_info_command,
    "/qa": handle_qa_command,
    "/support-summary": handle_support_summary_command,
    "/qa-versions": handle_qa_versions_command,
    "/qa-versions-update": handle_qa_versions_update_command,
}

def handle_socket_mode_request(client: SocketModeClient, request: SocketModeRequest):
    
    assert ALEXBOT_DB_URL, "ALEXBOT_DB_URL is not set"
    assert PORTAL_DB_URL, "PORTAL_DB_URL is not set"
    assert VESELKA_DB_URL, "VESELKA_DB_URL is not set"
    
    alexbot_db_connection = get_db_connection(ALEXBOT_DB_URL)
    portal_db_connection = get_db_connection(PORTAL_DB_URL)
    veselka_db_connection = get_db_connection(VESELKA_DB_URL)

    try:
        if request.type == "slash_commands":
            user_name = request.payload.get("user_name")
            channel_name = f"#{request.payload.get('channel_name')}"
            command = request.payload.get("command")
            message_ts = request.payload.get("ts")
            channel_id = request.payload.get("channel_id")
            command_text = request.payload.get("text", "").strip()
            
            assert user_name, "User name is required"
            assert channel_name, "Channel name is required"
            assert command, "Command is required"
            
            LOG.info(f"Received {command} command from user @{user_name} in channel {channel_name}")

            payload = request.payload
            if payload.get("command") == "/viz":                
                robot_id = payload.get("text", "")

                response_text = handle_viz_command(
                    client.web_client, alexbot_db_connection, portal_db_connection, channel_name, user_name, robot_id
                )

                response = SocketModeResponse(envelope_id=request.envelope_id, payload={"text": response_text})
                client.send_socket_mode_response(response)


            elif payload.get("command") == "/info":
                response_text = handle_info_command(
                    client.web_client, alexbot_db_connection, portal_db_connection, channel_name, user_name, command_text
                )

                if response_text:
                    response = SocketModeResponse(envelope_id=request.envelope_id, payload={"text": response_text})
                else:
                    response = SocketModeResponse(envelope_id=request.envelope_id, payload={})

                client.send_socket_mode_response(response)

            elif payload.get("command") == "/qa":
                success, response_text = handle_qa_command(
                    portal_db_connection, channel_name, user_name, command_text, channel_id, message_ts
                )

                if success:
                    client.web_client.chat_postMessage(channel=channel_name, text=response_text)
                    response = SocketModeResponse(envelope_id=request.envelope_id, payload={})
                else:
                    # Error case - send private error message
                    response = SocketModeResponse(envelope_id=request.envelope_id, payload={"text": response_text})

                client.send_socket_mode_response(response)

            elif payload.get("command") == "/support-summary":

                try:
                    success, response_messages = handle_support_summary_command(command_text)

                    if success:
                        # Success case - post all message chunks to channel
                        for message_chunk in response_messages:
                            client.web_client.chat_postMessage(channel=channel_name, text=message_chunk)
                        # Send empty acknowledgment
                        response = SocketModeResponse(envelope_id=request.envelope_id, payload={})
                    else:
                        # Error case - send private error message (should only be one message for errors)
                        error_text = response_messages[0] if response_messages else "❌ Unknown error"
                        response = SocketModeResponse(envelope_id=request.envelope_id, payload={"text": error_text})

                    client.send_socket_mode_response(response)

                except Exception as e:
                    LOG.error(f"Error handling /support-summary command: {e}")
                    traceback.print_exc()
                    error_response = SocketModeResponse(envelope_id=request.envelope_id, payload={"text": "❌ Error: Failed to generate support summary"})
                    client.send_socket_mode_response(error_response)

            elif payload.get("command") == "/qa-versions":
                success, response_text = handle_qa_versions_command(alexbot_db_connection, request)

                # Always send private message
                response = SocketModeResponse(envelope_id=request.envelope_id, payload={"text": response_text})
                client.send_socket_mode_response(response)
    
            elif payload.get("command") == "/qa-versions-update":

                success, response_text = handle_qa_versions_update_command(alexbot_db_connection, request)

                # Always send private message
                response = SocketModeResponse(envelope_id=request.envelope_id, payload={"text": response_text})
                client.send_socket_mode_response(response)

            else:
                response = SocketModeResponse(envelope_id=request.envelope_id, payload={"text": "❌ Unknown command"})
                client.send_socket_mode_response(response)
        else:
            response = SocketModeResponse(envelope_id=request.envelope_id, payload={"text": "❌ Invalid request type"})
            client.send_socket_mode_response(response)

    except Exception:
        response = SocketModeResponse(envelope_id=request.envelope_id, payload={"text": "❌ Internal server error"})
        client.send_socket_mode_response(response)
        traceback.print_exc()

    finally:
        alexbot_db_connection.close()
        portal_db_connection.close()
        veselka_db_connection.close()


def start_socket_mode_client():
    LOG.info("Starting Slack Socket Mode client...")
    client = SocketModeClient(app_token=SLACK_APP_TOKEN, web_client=WebClient(token=SLACK_TOKEN))
    client.socket_mode_request_listeners.append(handle_socket_mode_request)
    client.connect()
    LOG.info("Slack Socket Mode client started successfully")
